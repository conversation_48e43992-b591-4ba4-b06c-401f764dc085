"""
Dashboard routes for main application views and data.
"""

from fastapi import API<PERSON>out<PERSON>, Request, HTTPException, Depends, Query
from fastapi.responses import HTMLResponse
from fastapi.templating import Jinja2Templates
from typing import Optional, Dict, Any, List
from datetime import datetime, timedelta
import pandas as pd

from app.core.database import get_analyzer
from app.models.common import SuccessResponse, KPIData

router = APIRouter(tags=["Dashboard"])
templates = Jinja2Templates(directory="templates")


def format_dataframe_for_json(df: pd.DataFrame) -> List[Dict[str, Any]]:
    """Convert DataFrame to JSON-serializable format."""
    if df is None or df.empty:
        return []
    
    # Convert datetime columns to strings
    for col in df.select_dtypes(include=['datetime64[ns]', 'datetimetz']).columns:
        df[col] = df[col].dt.strftime('%Y-%m-%d %H:%M:%S')
    
    # Convert to dict and handle NaN values
    return df.fillna('').to_dict('records')


def calculate_kpis(df: pd.DataFrame) -> Dict[str, Any]:
    """Calculate KPIs from the main OF data."""
    if df is None or df.empty:
        return {
            "total_of": 0,
            "of_en_cours": 0,
            "of_termines": 0,
            "of_arretes": 0,
            "avg_prod": 0,
            "avg_temps": 0,
            "alertes": 0,
            "efficacite": 0
        }
    
    try:
        # Convert to numeric if needed
        df['Avancement_PROD'] = pd.to_numeric(df['Avancement_PROD'], errors='coerce')
        df['Avancement_temps'] = pd.to_numeric(df['Avancement_temps'], errors='coerce')
        df['EFFICACITE'] = pd.to_numeric(df['EFFICACITE'], errors='coerce')
        
        return {
            "total_of": len(df),
            "of_en_cours": len(df[df['STATUT'] == 'C']),
            "of_termines": len(df[df['STATUT'] == 'T']),
            "of_arretes": len(df[df['STATUT'] == 'A']),
            "avg_prod": float(df['Avancement_PROD'].mean() * 100) if not df['Avancement_PROD'].isna().all() else 0,
            "avg_temps": float(df['Avancement_temps'].mean() * 100) if not df['Avancement_temps'].isna().all() else 0,
            "alertes": int(df['Alerte_temps'].sum()) if 'Alerte_temps' in df.columns else 0,
            "efficacite": float(df[(df['EFFICACITE'] > 0) & (df['EFFICACITE'] < 5)]['EFFICACITE'].mean()) if 'EFFICACITE' in df.columns else 0
        }
    except Exception as e:
        print(f"Error calculating KPIs: {e}")
        return {
            "total_of": len(df),
            "of_en_cours": 0,
            "of_termines": 0,
            "of_arretes": 0,
            "avg_prod": 0,
            "avg_temps": 0,
            "alertes": 0,
            "efficacite": 0
        }


@router.get("/", response_class=HTMLResponse)
async def dashboard(request: Request):
    """Main dashboard page."""
    return templates.TemplateResponse("dashboard.html", {"request": request})


@router.get("/api/dashboard-data", response_model=SuccessResponse)
async def get_dashboard_data(
    date_debut: Optional[str] = Query(None, description="Start date (YYYY-MM-DD)"),
    date_fin: Optional[str] = Query(None, description="End date (YYYY-MM-DD)"),
    statut_filter: Optional[str] = Query(None, description="Status filter (C/T/A)"),
    analyzer=Depends(get_analyzer)
):
    """Get all dashboard data including KPIs and detailed information."""
    try:
        # Set default dates if not provided
        if not date_debut:
            date_debut = (datetime.now() - timedelta(days=90)).strftime('%Y-%m-%d')
        if not date_fin:
            date_fin = datetime.now().strftime('%Y-%m-%d')
        
        # Get all data
        dashboard_data = analyzer.get_dashboard_data(date_debut, date_fin, statut_filter)
        
        main_of_data = dashboard_data.get('main_of_data')
        charge_data = dashboard_data.get('charge_data')
        backlog_data = dashboard_data.get('backlog_data')
        personnel_data = dashboard_data.get('personnel_data')
        
        # Calculate KPIs
        kpis = calculate_kpis(main_of_data)
        
        return SuccessResponse(data={
            "kpis": kpis,
            "data": {
                "main_of": format_dataframe_for_json(main_of_data),
                "charge": format_dataframe_for_json(charge_data),
                "backlog": format_dataframe_for_json(backlog_data),
                "personnel": format_dataframe_for_json(personnel_data)
            },
            "filters": {
                "date_debut": date_debut,
                "date_fin": date_fin,
                "statut_filter": statut_filter
            }
        })
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching dashboard data: {str(e)}")


@router.get("/api/kpis", response_model=SuccessResponse)
async def get_kpis(
    date_debut: Optional[str] = Query(None, description="Start date (YYYY-MM-DD)"),
    date_fin: Optional[str] = Query(None, description="End date (YYYY-MM-DD)"),
    statut_filter: Optional[str] = Query(None, description="Status filter (C/T/A)"),
    analyzer=Depends(get_analyzer)
):
    """Get KPIs only for dashboard widgets."""
    try:
        # Set default dates if not provided
        if not date_debut:
            date_debut = (datetime.now() - timedelta(days=90)).strftime('%Y-%m-%d')
        if not date_fin:
            date_fin = datetime.now().strftime('%Y-%m-%d')
        
        # Get main OF data
        df = analyzer.get_comprehensive_of_data(date_debut, date_fin, statut_filter)
        
        # Calculate KPIs
        kpis = calculate_kpis(df)
        
        return SuccessResponse(data={
            "kpis": kpis,
            "period": {
                "date_debut": date_debut,
                "date_fin": date_fin,
                "statut_filter": statut_filter
            }
        })
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching KPIs: {str(e)}")


@router.get("/api/summary-stats", response_model=SuccessResponse)
async def get_summary_stats(
    date_debut: Optional[str] = Query(None),
    date_fin: Optional[str] = Query(None),
    analyzer=Depends(get_analyzer)
):
    """Get summary statistics for the dashboard."""
    try:
        # Set default dates if not provided
        if not date_debut:
            date_debut = (datetime.now() - timedelta(days=90)).strftime('%Y-%m-%d')
        if not date_fin:
            date_fin = datetime.now().strftime('%Y-%m-%d')

        # Get all data
        dashboard_data = analyzer.get_dashboard_data(date_debut, date_fin, None)

        main_of_data = dashboard_data.get('main_of_data')
        charge_data = dashboard_data.get('charge_data')
        backlog_data = dashboard_data.get('backlog_data')
        personnel_data = dashboard_data.get('personnel_data')

        # Calculate summary statistics
        summary = {
            "of_stats": {
                "total": len(main_of_data) if main_of_data is not None else 0,
                "en_cours": len(main_of_data[main_of_data['STATUT'] == 'C']) if main_of_data is not None else 0,
                "termines": len(main_of_data[main_of_data['STATUT'] == 'T']) if main_of_data is not None else 0,
                "arretes": len(main_of_data[main_of_data['STATUT'] == 'A']) if main_of_data is not None else 0
            },
            "charge_stats": {
                "total_secteurs": len(charge_data) if charge_data is not None else 0,
                "total_operateurs": int(charge_data['NB_OPERATEURS'].sum()) if charge_data is not None else 0,
                "heures_disponibles": int(charge_data['NB_HEURES_DISPONIBLES_SEMAINE'].sum()) if charge_data is not None else 0
            },
            "backlog_stats": {
                "total_of": len(backlog_data) if backlog_data is not None else 0,
                "urgent": len(backlog_data[backlog_data['PRIORITE'] == 'URGENT']) if backlog_data is not None else 0,
                "prioritaire": len(backlog_data[backlog_data['PRIORITE'] == 'PRIORITAIRE']) if backlog_data is not None else 0
            },
            "personnel_stats": {
                "total_actifs": len(personnel_data) if personnel_data is not None else 0,
                "qualifications_uniques": personnel_data['QUALIFICATION'].nunique() if personnel_data is not None else 0
            }
        }

        return SuccessResponse(data={
            "summary": summary,
            "period": {
                "date_debut": date_debut,
                "date_fin": date_fin
            }
        })
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching summary stats: {str(e)}")


@router.get("/api/filters/options", response_model=SuccessResponse)
async def get_filter_options(analyzer=Depends(get_analyzer)):
    """Get available filter options for dropdowns."""
    try:
        # Get a sample of data to extract filter options
        date_debut = (datetime.now() - timedelta(days=90)).strftime('%Y-%m-%d')
        date_fin = datetime.now().strftime('%Y-%m-%d')

        df = analyzer.get_comprehensive_of_data(date_debut, date_fin, None)

        if df is None or df.empty:
            return SuccessResponse(data={
                "options": {
                    "familles": [],
                    "clients": [],
                    "statuts": ["C", "T", "A"]
                }
            })

        # Extract unique values for filters
        familles = sorted(df['FAMILLE_TECHNIQUE'].dropna().unique().tolist())
        clients = sorted(df['CLIENT'].dropna().unique().tolist())

        return SuccessResponse(data={
            "options": {
                "familles": familles,
                "clients": clients,
                "statuts": ["C", "T", "A"]
            }
        })
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching filter options: {str(e)}")
